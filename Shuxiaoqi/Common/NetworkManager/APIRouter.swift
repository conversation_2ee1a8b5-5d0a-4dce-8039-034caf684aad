/// API路由层
enum APIRouter {
    // 用户相关
    enum User {
        //保存用户兴趣标签
        static func setUserLabels(ids:[Int]) -> APIRequest {
            APIRequest(
                path: "/api/video/label/setUserLabels",
                method: .post,
                parameters: ["ids":ids]
            )
        }
        
        //用户兴趣标签
        static var getUserLabel:APIRequest {
            APIRequest(
                path: "/api/video/label/listLabelGroup",
                method: .get,
                parameters: [:]
            )
        }
        
        
        //获取用户互动管理配置
        static var getUserInteractionSet: APIRequest {
            APIRequest(
                path: "/api/video/userInteraction/getUserInteractionSet",
                method: .get,
                parameters: [:]
            )
        }
        
        //修改用户互动管理配置
        static func setUserInteractionSet(interactionConfigId: Int, interactiveSettingButton: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/userInteraction/setUserInteractionSet",
                method: .post,
                parameters: [
                    "interactionConfigId" :interactionConfigId,
                    "interactiveSettingButton" : interactiveSettingButton
                ]
            )
        }
        
        static func login(phone: String, code: String, inviteCode: Int = 0) -> APIRequest {
            APIRequest(
                path: "/api/video/user/login.do",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "code": code,
                    "inviteCode": inviteCode,
                    "phone": phone
                ]
            )
        }
        
        static func getSMSCode(phone: String, captchaCode: String, codeId: String, type: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/smcCode.do",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "captchaCode": captchaCode,
                    "phoneNumber": phone,
                    "codeId": codeId,
                    "type": type
                ]
            )
        }
        
        static func getCommonMsg(captchaCode: String, codeId: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/sendCommonMsg",
                method: .get,
                parameters: [
                    "appType": "ys_video",
                    "captchaCode": captchaCode,
                    "codeId": codeId,
                ]
            )
        }
        
        static func checkCommonMsg(code: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/checkCommonMsg",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "code" : code,
                ]
            )
        }
        
        static func bangPhone(phone: String, code: String, checkId: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/bangPhone",
                method: .post,
                parameters: [
                    "appType" : "ys_video",
                    "checkId" : checkId,
                    "code" : code,
                    "phone" : phone,
                ]
            )
        }
        
        static func bingUserPhone(bingPhoneId: String, code: String, phone: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/bingUserPhone.do",
                method: .post,
                parameters: [
                    "appType" : "ys_video",
                    "bingPhoneId": bingPhoneId,
                    "code": code,
                    "inviteCode": 0,
                    "phone": phone
                ]
            )
        }
            
        
        static func getCaptcha(codeId: String, type: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/smsCodeBeforeCaptcha.do",
                method: .get,
                parameters: [
                    "appType": "ys_video",
                    "codeId": codeId,
                    "type": type
                ]
            )
        }
        
        //密码登录
        static func loginWithPassword(phone: String, password: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/passwordLogin.do",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "password": password,
                    "phone": phone,
                ]
            )
        }
        
        // 新增：获取当前用户脱敏手机号
        static var getUserPhone: APIRequest {
            APIRequest(
                path: "/api/video/user/getUserPhone",
                method: .get,
                parameters: [:] // 公共参数中包含 token
            )
        }
        
        // 密码更换
        static func changePassword(checkId: String, passWord: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/pwdChange",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "checkId": checkId,
                    "passWord": passWord
                ]
            )
        }
        
        //首次进入app请求
        static func firstEnter(deviceId: String, deviceName:String, deviceSystem:String, deviceType:Int, loginAddress:String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/loadingData",
                method: .post,
                parameters: [
                    "deviceId": deviceId,
                    "deviceName": deviceName,
                    "deviceSystem": deviceSystem,
                    "deviceType": deviceType,
                    "loginAddress": loginAddress
                ] // 公共参数中包含 token
            )
        }

        //获取登录设备
        static func getLoginDevice() -> APIRequest {
            APIRequest(
                path: "/api/video/user/getLoginDevice",
                method: .get,
                parameters: [:]
            )
        }
        
        //一键登录
        static func loginOneTouch(accessToken: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/loginOneTouch.do",
                method: .post,
                parameters: [
                    "accessToken":accessToken,
                    "appType": "ys_video",
                    "inviteCode": ""
                ]
            )
        }
        
        //忘记密码-验证短信
        static func checkUserPwdMsg(phone: String, code: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/checkUserPwdMsg.do",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "code": code,
                    "phone": phone
                ]
            )
        }
        
        //忘记密码-绑定密码
        static func bingUserPwd(phone: String, checkId: String, password: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/bingUserPwd.do",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "checkId": checkId,
                    "phone": phone,
                    "password": password
                ]
            )
        }
        
        //退出登录
        static var logout: APIRequest {
            APIRequest(
                path: "/api/video/user/outLogin",
                method: .post,
                parameters: [:] // 公共参数中包含 token
            )
        }

        //注销账号
        static func logoutUser(logoutCode: String, logoutRemark: String, logoutType: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/user/logoutUser",
                method: .post,
                parameters: [
                    "logoutCode": logoutCode,
                    "logoutRemark": logoutRemark,
                    "logoutType": logoutType
                ]
            )
        }
        
        //获取设置信息
        static var getAccountSetInfo: APIRequest {
            APIRequest(
                path: "/api/video/user/getAccountSetInfo",
                method: .get,
                parameters: [:] // 公共参数中包含 token
            )
        }
        
        //获取通知列表
        static var getUserNoticeSet: APIRequest {
            APIRequest(
                path: "/api/video/user/getUserNoticeSet",
                method: .get,
                parameters: [:] // 公共参数中包含 token
            )
        }
        
        //设置通知列表（新版，支持按钮和配置项二选一）
        static func setUserNoticeSetV2(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/video/user/setUserNoticeSet",
                method: .post,
                parameters: params // 公共参数中包含 token
            )
        }
        
        // 微信登录
        static func wechatLogin(code: String) -> APIRequest {
            APIRequest(
                path: "/api/video/user/loginOauth2Wx.do",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "code": code
                ]
            )
        }

        // 微信绑定
        static func bindWechat(code: String, isOperateBind: Bool) -> APIRequest {
            APIRequest(
                path: "/api/video/user/bindWx",
                method: .post,
                parameters: [
                    "appType": "ys_video",
                    "code": code,
                    "isOperateBind": isOperateBind
                ]
            )
        }

        // 苹果登录
        static func appleLogin(accessToken: String, inviteCode: Int? = nil) -> APIRequest {
            var parameters: [String: Any] = [
                "identityToken": accessToken,
                "appType": "ys_video"
            ]

            if let inviteCode = inviteCode {
                parameters["inviteCode"] = inviteCode
            }

            return APIRequest(
                path: "/api/video/user/appleLoginOneTouch.do",
                method: .post,
                parameters: parameters
            )
        }
    }
    
    //
    enum Center {
        //获取用户信息
        static var getUserInfo: APIRequest {
            APIRequest(
                path: "/api/video/center/getUserInfo",
                method: .get,
                parameters: [:] // 公共参数中包含 token
            )
        }
        
        //推荐关注的用户
        static var getAttention: APIRequest {
            APIRequest(
                path: "/api/video/center/recommendFollowUser",
                method: .get,
                parameters: [:] // 公共参数中包含 token
            )
        }
        
        //分页查询黑名单
        static func getBlackList(page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/center/pageSvUserBlack",
                method: .get,
                parameters: ["page":page, "size": size] // 公共参数中包含 token
            )
        }
        
        //获取关注我的用户(我的粉丝)
        static func getFollowMyUser(page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/center/pageFollowMyUser",
                method: .get,
                parameters: ["page":page, "size": size] // 公共参数中包含 token
            )
        }
        
        //获取我关注的用户（关注列表）
        static func getMyAttention(page: Int, size: Int, name: String) -> APIRequest {
            APIRequest(
                path: "/api/video/center/pageMyFollowUser",
                method: .get,
                parameters: ["page":page, "size": size, "name": name] // 公共参数中包含 token"] // 公共参数中包含 token
            )
        }
        
        //关注用户
        static func followUser‌(customerId: String, type: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/userFollowUp",
                method: .post,
                parameters: ["customerId": customerId, "type": type] // 公共参数中包含 token
            )
        }
        
        //拉黑用户
        static func doUserBlock(customerId: String) -> APIRequest {
            APIRequest(
                path: "/api/video/center/doUserBlock",
                method: .post,
                parameters: ["customerId":customerId] // 公共参数中包含 token
            )
        }
        
        //移除黑名单
        static func clearUserBlock(customerId: String) -> APIRequest {
            APIRequest(
                path: "/api/video/center/clearUserBlock",
                method: .post,
                parameters: ["customerId":customerId] // 公共参数中包含 token
            )
        }
        
        //获取黑名单数量
        static func getSvUserBlackCount() -> APIRequest {
            APIRequest(path: "/api/video/center/getUserBlockCount", method: .get, parameters: [:])
        }
        
        //查询用户点赞视频()
        static func getUserWorksLike(page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/center/pageUserWorksLike",
                method: .get,
                parameters: ["page": page, "size": size] // 公共参数中包含 token
            )
        }
        
        //操作点赞和收藏
        //operateType    操作类型 1-点赞 2-收藏
        //operateValue    操作值 1-执行 2-取消
        //worksIds    作品ids
        static func doWorksLikeAndCollect(operateValue: Int, operateType: Int, worksIds: [String]) -> APIRequest {
            APIRequest(
                path: "/api/video/main/userOperateWorks",
                method: .post,
                parameters: ["operateValue": operateValue, "operateType": operateType, "worksIds": worksIds] // 公共参数中包含 token
            )
            
        }
        
        //查询用户收藏的视频
        static func getUserWorksCollect(page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/center/pageUserWorksCollect",
                method: .get,
                parameters: ["page": page, "size": size] // 公共参数中包含 token
            )
        }
    }
    
    enum PersonHome {
        //个人主页-信息
        static func getPersonHomeInfo(customerId: String) -> APIRequest {
            APIRequest(
                path: "/api/video/main/getPersonHomeInfo",
                method: .get,
                parameters: ["customerId" : customerId] //用户id
            )
        }
        
        //个人主页-作品
        static func getPersonHomeWorks(customerId: String, page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/getPersonHomeWorks",
                method: .get,
                parameters: [
                    "customerId" : customerId,
                    "page": page,
                    "size": size
                ]
            )
        }
        
        //个人主页-收藏
        static func getPersonHomeWorksCollect(customerId: String, page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/getPersonHomeWorksCollect",
                method: .get,
                parameters: [
                    "customerId" : customerId,
                    "page": page,
                    "size": size
                ]
            )
        }
        
        //个人主页-喜欢
        static func getPersonHomeWorksLike(customerId: String, page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/getPersonHomeWorksLike",
                method: .get,
                parameters: [
                    "customerId" : customerId,
                    "page": page,
                    "size": size
                ]
            )
        }
    }
    
    enum Main {
        //获取更新弹窗信息
        //appVersion+
        static func getAppVersion(appVersion: String) -> APIRequest {
            APIRequest(
                path: "/api/video/config/listAppVersion.do",
                method: .get,
                parameters: ["appVersion":appVersion, "systemType":"2"]
            )
        }

        ///api/video/appHomeConfig/getList
        //app首页选项列表
        //type    App 首页设置类型 0-tabbar（替换bar） 1-tab
        static func getAppHomeConfigList(type: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/appHomeConfig/getList",
                method: .get,
                parameters: ["type": type]
            )
        }
        
        //侧边栏-用户信息
        static var getUserMain:APIRequest {
            APIRequest(
                path: "/api/video/main/userMain",
                method: .get,
                parameters: [:]
            )
        }
        
        //个人中心-收藏
        static func getPersonHomeWorksCollect(customerId: String, page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/getPersonHomeWorksCollect",
                method: .get,
                parameters: [
                    "customerId": customerId,
                    "page": page,
                    "size": size,
                ]
            )
        }
        
        //获取用户资料
        static var getCustomerInfo:APIRequest {
            APIRequest(
                path: "/api/video/customer/getCustomerInfo",
                method: .get,
                parameters: [:]
            )
        }
        
        //保存用户资料
        ///api/video/customer/setUserInfo
        static func setUserInfo(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/video/customer/setUserInfo",
                method: .post,
                parameters: params
            )
        }
        
        //保存用户头像
        static func setUserAvatar(AvatarUrl: String) -> APIRequest {
            APIRequest(
                path: "/api/video/customer/updateAvatar",
                method: .post,
                parameters: ["url": AvatarUrl]
            )
            
        }

        //获取地区选项
        static func getRegionOption(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/store/region/getOption",
                method: .get,
                parameters: params
            )
        }
            
    }
    
    // 搜索相关
    enum Search {
        //搜索作品
        static func searchWorksMain(keywords: String, page: Int, size: Int, sort: Int? = nil) -> APIRequest {
            var params: [String: Any] = ["keywords": keywords, "page": page, "size": size]
            if let sort = sort {
                params["sort"] = sort
            }
            return APIRequest(
                path: "/api/video/main/searchWorksMain.do",
                method: .get,
                parameters: params // 公共参数中包含 token
            )
        }
        
        //关键字搜索用户
        static func searchUser(keywords: String, page: Int, size: Int, isIgnore: Bool? = nil) -> APIRequest {
            var parameters: [String: Any] = ["keywords":keywords, "page":page, "size":size]
            if let isIgnore = isIgnore {
                parameters["isIgnore"] = isIgnore
            }
            return APIRequest(
                path: "/api/video/user/searchUser",
                method: .get,
                parameters: parameters
            )
        }

        //搜索团购+外卖
        ///api/userShop/userGetShopList.do
        //searchValue 搜索条件
        //latitude 纬度（必传）
        //longitude 经度（必传）
        //provinceId 省份id
        //cityId 城市id
        //areaId 区县id
        //type 类型 0-团购 1-外卖（必传）
        //page 页码（必传） 0_n
        //size 每页条数（必传） 默认10
        //smartBy 智能排序查询条件,1:附近，2:距离最近,,3:人气最高,4:售量最高,5:价格最低
        //categoryId 分类查询条件
        //priceNum 价格筛选 1-10元以下 2-10~20元 3-20~50元 4-50元以上
        //distance 距离筛选 1-1公里以内 2-1~3公里 3-3公里以上
        //除了必传参数，其他参数可以不传
        static func searchShop(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/userShop/userGetShopList.do",
                method: .get,
                parameters: params
            )
        }

        //商品搜索
        ///api/goods/v1/searchGood.do
        //keywords 搜索条件
        //page  页码 (0..N)
        //size  每页显示的数目
        //sort  以下列格式排序标准：property[,asc | desc]。 默认排序顺序为升序。 支持多种排序条件：如：id,asc
        //categoryId
        //searchType
        static func searchGood(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/goods/v1/searchGood.do",
                method: .get,
                parameters: params
            )
        }
        
        //搜索大学名称
        static func searchSchool(name: String) -> APIRequest {
            APIRequest(
                path: "/api/store/school/search",
                method: .get,
                parameters: ["name":name]
            )
        }

        //搜索商品历史
        ///api/video/main/listSearchHistory
        static func listSearchHistory(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/video/main/listSearchHistory",
                method: .get,
                parameters: params
            )
        }

        //搜索热词
        ///api/video/main/listSearchRecommend.do
        static func listSearchRecommend(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/video/main/listSearchRecommend.do",
                method: .get,
                parameters: params
            )
        }
    }
    
    // 视频相关
    enum Video {
        // MARK: - 视频推流接口（已登录）
        // 获取视频流 - 登录用户
        // loadNumber: 加载数量，推荐流固定10条（全部由后端计算）
        static func getVideoStream(loadNumber: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/worksInfoListMain",
                method: .get,
                parameters: ["loadNumber": loadNumber]
            )
        }

        // MARK: - 视频推流接口（未登录）
        // 获取视频流 - 未登录用户
        // deviceId: 设备ID，loadNumber: 加载数量
        static func getVideoStream_unLogin(deviceId: String, loadNumber: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/worksInfoListMain.do",
                method: .get,
                parameters: ["deviceId": deviceId, "loadNumber": loadNumber]
            )
        }

        //获取播放签名
        static func getPlaySign(videoId: Int) -> APIRequest {
            APIRequest(
                path: "/api/txVideo/sv/getPlaySign",
                method: .post,
                parameters: ["videoId": videoId]
            )
        }

        //获取指定视频详情
        static func getVideoDetail(videoId: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/getWorks",
                method: .get,
                parameters: ["id": videoId]
            )
        }

        ///api/video/main/mainWorksInfo.do
        //获取首页推荐视频 worksCategoryId不传=推荐
        static func getMainWorksInfo(worksCategoryId: Int? = nil) -> APIRequest {
            APIRequest(
                path: "/api/video/main/mainWorksInfo.do",
                method: .get,
                parameters: [
                    "worksCategoryId":worksCategoryId ?? ""
                ]//不强制传
            )
        }
        
        // MARK: - 获取关注作品列表
        // loadNumber: 加载数量，5(首次)、10(缓存<20)、20(缓存<30)
        static func getFollowWorksList(loadNumber: Int) -> APIRequest {
            let fullPath = "/api/video/main/worksInfoListMainFollow?loadNumber=\(loadNumber)"
            return APIRequest(
                path: fullPath,
                method: .get,
                parameters: [:]
            )
        }

        // MARK: - 获取朋友作品列表
        // loadNumber: 加载数量，固定10条
        static func getFriendWorksList(loadNumber: Int) -> APIRequest {
            let fullPath = "/api/video/main/worksInfoListMainFollowEachOther?loadNumber=\(loadNumber)"
            return APIRequest(
                path: fullPath,
                method: .get,
                parameters: [:]
            )
        }
        
        // MARK: - 获取同城作品列表
        // loadNumber: 加载数量，后端修改了加载方案，不需要手动控制缓存，每次请求都会由后台计算是否加载过，我们一直加载10条即可
        static func getCityWorksList(loadNumber: Int, areaCode: String) -> APIRequest {
            let fullPath = "/api/video/main/worksInfoListMainCity"
            return APIRequest(
                path: fullPath,
                method: .get,
                parameters: ["loadNumber":loadNumber, "areaCode": areaCode]
            )
        }
        
        // MARK: - 获取未登录同城作品列表
        // deviceId: 设备id，loadNumber: 加载数量（后端修改了加载方案，不需要手动控制缓存，我们一直加载10条即可），areaCode: 市区id
        static func getMainCityWorksList(deviceId: String, loadNumber: Int, areaCode: String) -> APIRequest {
            let fullPath = "/api/video/main/worksInfoListMainCity.do?deviceId=\(deviceId)&loadNumber=\(loadNumber)&areaCode=\(areaCode)"
            return APIRequest(
                path: fullPath,
                method: .get,
                parameters: [:]
            )
        }
        
        /// 获取腾讯云VOD上传签名
        static var getVodSignature: APIRequest {
            APIRequest(
                path: "/api/txVideo/sv/getSignature",
                method: .get,
                parameters: [:]
            )
        }
        
        //发布视频
        ///api/video/center/shortVideoWorksAdd
        static func shortVideoWorksAdd(params: [String: Any]) -> APIRequest {
            APIRequest(
                path: "/api/video/center/shortVideoWorksAdd",
                method: .post,
                parameters: params
            )
        }
        
        //保存到草稿箱
        ///api/video/center/shortVideoWorksDraftsAdd
        static func shortVideoWorksDraftsAdd(params: [String : Any]) -> APIRequest {
            APIRequest(
                path: "/api/video/center/shortVideoWorksDraftsAdd",
                method: .post,
                parameters: params
            )
        }
        
        //查询草稿箱列表
        static func getVideoWorksDraftsList(params: [String : Any]) -> APIRequest {
            APIRequest(
                path: "/api/video/center/shortVideoWorksDraftsPage",
                method: .get,
                parameters: params
            )
        }
        
        //删除草稿箱中的内容
        ///api/video/center/shortVideoWorksDraftsDelete
        static func shortVideoWorksDraftsDelete(ids: [Int]) -> APIRequest {
            // 将 ids 数组转换为逗号分隔的字符串，用于 URL Query
            let idsString = ids.map { String($0) }.joined(separator: ",")
            
            // 构建带有查询参数的完整路径
            let fullPath = "/api/video/center/shortVideoWorksDraftsDel?ids=\(idsString)"
            
            return APIRequest(
                path: fullPath,
                method: .post,
                parameters: [:] // 参数已放入 URL Query，请求体为空或只包含其他可能的参数
            )
        }
        
        //分类列表
        ///api/video/type/list
        static func getVideoTypeList(isHomeShow: Bool)-> APIRequest {
            APIRequest(
                path: "/api/video/type/list",
                method: .get,
                parameters: ["isHomeShow": isHomeShow]
            )
        }
        
        /// 上传文件至七牛云
        /// 调用方应通过 multipart/form-data 方式在表单字段 "file" 中携带文件数据
        static var uploadFileQN: APIRequest {
            APIRequest(
                path: "/api/qiNiuContent",
                method: .post,
                parameters: [:] // 参数通过 multipart/form-data 进行组装
            )
        }
        
        
        
        
        /// 获取腾讯云VOD上传签名
//        static var getVideoTypeList: APIRequest {
//            APIRequest(
//                path: "/video/api/video/type/list",
//                method: .get,
//                parameters: [:]
//            )
//        }
        
        //分裂按钮被单机的对应UI
        
        
        //展示UI列表的iew
        
        
        //
        
        //创作中心列表-当前用户发布过的视频管理
//        duration    视频时长类型 1-60秒已下 2-1~3分钟 3-3分钟以上
//        keywords 关键字，用于搜索
//        sort  排序
//        state    状态 1-审核中 2-已上架 3-已下架 4-审核失败
//        time    发布时间 1-今天 2-本周 3-本月
//        worksCategoryId    视频分类id
        static func getPersonalWorksList(page: Int, size: Int, duration: Int? = nil, keywords: String? = nil, sort: Int? = nil, state: Int? = nil, time: Int? = nil, worksCategoryId: String? = nil) -> APIRequest {
            var params: [String: Any] = ["page": page, "size": size]
            
            if let duration = duration {
                params["duration"] = duration
            }
            if let keywords = keywords {
                params["keywords"] = keywords
            }
            if let sort = sort {
                params["sort"] = sort
            }
            if let state = state {
                params["state"] = state
            }
            if let time = time {
                params["time"] = time
            }
            if let worksCategoryId = worksCategoryId {
                params["worksCategoryId"] = worksCategoryId
            }
            
            return APIRequest(
                path: "/api/video/center/shortVideoWorksPage",
                method: .get,
                parameters: params // 公共参数中包含 token
            )
        }
        
        //内容管理-操作
        //type 操作类型 1-下架 2-上架 3-删除
        //worksIds 作品id集合
        static func shortVideoWorksOperate(type: Int, worksIds: [String]) -> APIRequest {
            APIRequest(
                path: "/api/video/center/shortVideoWorksOperate",
                method: .post,
                parameters: ["type": type, "worksIds": worksIds] // 公共参数中包含 token
            )
        }

        //添加观看记录
        //worksId 作品id
        static func addWorksWatch(worksId: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/addWorksWatch",
                method: .post,
                parameters: ["worksId": String(worksId)], // 公共参数中包含 token
                encoding: .json
            )
        }
    }
    
    enum Config {
        
    }
    
    enum Effects {
        
        //    类型 1-特效 2-美颜 4-滤镜
        //配置-美颜
        static func getVideoEditConfig(type: String) -> APIRequest {
            APIRequest(
                path: "/api/video/config/listVideoEditConfig.do",
                method: .get,
                parameters: [
                    "type": type
                ]
            )
        }
        
        //查询音乐
        static func getVideoEditMusicList(collect: Bool, page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/pageUserMusic",
                method: .get,
                parameters: [
                    "collect": collect,
                    "page": page,
                    "size": size
                ]
            )
        }
        
        //操作音乐  操作 1-收藏 2-取消
        static func videoEditCollectingMusic(id: Int, operate: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/operateUserMusic",
                method: .post,
                parameters: [
                    "id": id,
                    "operate": operate,
                ]
            )
        }
    }

    // 评论
    enum Comment {
        //获取视频评论
        ///api/video/main/pageWorkComment
        //pid    评论id(查询二级评论)
        //excludeId    排除的评论id(当有指定评论，分页查询时影藏此评论)
        //fromId    最后一条评论id
        //createTime    最后一条评论时间
        //用最后一条评论id和时间来分页
        static func getVideoComment(worksId: Int, size: Int, pid: Int? = nil, excludeId: Int? = nil, fromId: Int? = nil, createTime: String? = nil) -> APIRequest {
            APIRequest(
                path: "/api/video/main/pageWorkComment",
                method: .get,
                parameters: [
                    "worksId": worksId,
                    "size": size,
                    "pid": pid ?? "",
                    "excludeId": excludeId ?? "",
                    "fromId": fromId ?? "",
                    "createTime": createTime ?? ""
                ]
            )
        }

        //获取用户历史评论
        static func getCommentList(page: Int, size: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/center/pageUserWorksComment",
                method: .get,
                parameters: ["page": page, "size": size]
            )
        }

        //发送评论
        //其他非必传参数
        //address 地址
        //commentImg 表情/图片数组集合 JSON格式
        //lat 纬度
        //lng 经度
        static func sendComment(worksId: Int, commentDesc: String, address: String? = nil, commentImg: [String]? = nil, lat: String? = nil, lng: String? = nil) -> APIRequest {
            APIRequest(
                path: "/api/video/main/userDoCommentWork",
                method: .post,
                parameters: ["worksId": worksId, "commentDesc": commentDesc, "address": address, "commentImg": commentImg, "lat": lat, "lng": lng]
            )
        }

        // 回复评论
        // worksId 视频ID (必传)
        // pid     被回复的评论ID (必传)
        // commentDesc 回复内容 (必传)
        // 其余参数同发送评论，可选
        static func replyComment(worksId: Int, pid: Int, commentDesc: String, address: String? = nil, commentImg: [String]? = nil, lat: String? = nil, lng: String? = nil, pcustomerId: String? = nil) -> APIRequest {
            APIRequest(
                path: "/api/video/main/userReplyCommentWork",
                method: .post,
                parameters: [
                    "worksId": worksId,
                    "pid": pid,
                    "commentDesc": commentDesc,
                    "address": address ?? "",
                    "commentImg": commentImg ?? [],
                    "lat": lat ?? "",
                    "lng": lng ?? "",
                    "pcustomerId": pcustomerId ?? ""
                ]
            )
        }

        //删除评论-传入Query
        static func deleteComment(commentId: Int) -> APIRequest {
            // 将 commentId 作为查询参数放入 URL
            let fullPath = "/api/video/center/deleteUserWorksComment?id=\(commentId)"
            return APIRequest(
                path: fullPath,
                method: .post,
                parameters: [:] // 参数已放入 URL Query，请求体为空或只包含其他可能的参数
            )
        }

        //评论点赞，点踩
        //main/operateWorkComment
        /*{
            "id": 0,评论id
            "operateType": 0,操作类型 1-点赞 2-不喜欢 3-举报
            "operateValue": 0,操作值 1-执行 2-取消
            "worksId": 0    视频id
        }
        */
        static func operateWorkComment(commentId: Int, operateType: Int, operateValue: Int, worksId: Int) -> APIRequest {
            APIRequest(
                path: "/api/video/main/operateWorkComment",
                method: .post,
                parameters: ["id": commentId, "operateType": operateType, "operateValue": operateValue, "worksId": worksId]
            )
        }
    }
}
